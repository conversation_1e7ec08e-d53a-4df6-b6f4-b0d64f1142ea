{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/components/ui/button.tsx"], "sourcesContent": ["// src/components/ui/button.tsx\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { Loader2 } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:shrink-0 [&_svg]:transition-colors [&_svg]:duration-200\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-black text-white hover:bg-black hover:text-white\",\n        destructive: \"bg-black text-white hover:bg-black hover:text-white\",\n        outline: \"bg-black text-white hover:bg-black hover:text-white border border-black\",\n        secondary: \"bg-black text-white hover:bg-black hover:text-white\",\n        ghost: \"bg-black text-white hover:bg-black hover:text-white\",\n        link: \"bg-black text-white hover:bg-black hover:text-white underline-offset-4\",\n      },\n      size: {\n        default: \"h-9 px-4\",\n        sm: \"h-8 px-3 text-sm\",\n        lg: \"h-10 px-6 text-base\",\n        icon: \"h-9 w-9 p-2 bg-black text-white hover:bg-black hover:text-white\",\n      },\n      loading: {\n        true: \"relative text-transparent [&_svg]:absolute [&_svg]:inset-0\",\n        false: \"\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      loading: false,\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant,\n      size,\n      loading = false,\n      asChild = false,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n\n    return (\n      <Comp\n        ref={ref}\n        className={cn(buttonVariants({ variant, size, loading, className }))}\n        aria-busy={loading}\n        disabled={loading || props.disabled}\n        {...props}\n      >\n        {loading ? <Loader2 className=\"animate-spin h-4 w-4\" aria-hidden /> : null}\n        <span className={loading ? \"opacity-0\" : \"\"}>{children}</span>\n      </Comp>\n    )\n  }\n)\n\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;AAC/B;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,iUACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CACE,EACE,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,KAAK,EACf,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAS;QAAU;QACjE,aAAW;QACX,UAAU,WAAW,MAAM,QAAQ;QAClC,GAAG,KAAK;;YAER,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;gBAAuB,aAAW;;;;;uBAAM;0BACtE,8OAAC;gBAAK,WAAW,UAAU,cAAc;0BAAK;;;;;;;;;;;;AAGpD;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground\",\n        \"flex flex-col gap-6 rounded-xl border border-gray-700 py-6 shadow-lg\",\n        \"transform transition-transform duration-200 hover:scale-105\",\n        \"animate-fadeInUp\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"text-2xl font-bold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-card-muted text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAGA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,wEACA,+DACA,oBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-card-muted text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-card-muted selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-black flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-black focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 dark:focus-visible:ring-offset-card\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6bACA,4IACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/data/quotes.tsx"], "sourcesContent": ["export interface Quote {\n  id: number;\n  text: string;\n  author: string;\n  category: string;\n}\n\nexport const quotesData: Quote[] = [\n  {\n    id: 1,\n    text: \"The only way to do great work is to love what you do.\",\n    author: \"<PERSON>\",\n    category: \"motivation\"\n  },\n  {\n    id: 2,\n    text: \"Life is what happens to you while you're busy making other plans.\",\n    author: \"<PERSON>\",\n    category: \"life\"\n  },\n  {\n    id: 3,\n    text: \"The future belongs to those who believe in the beauty of their dreams.\",\n    author: \"<PERSON>\",\n    category: \"success\"\n  },\n  {\n    id: 4,\n    text: \"It is during our darkest moments that we must focus to see the light.\",\n    author: \"Aristotle\",\n    category: \"motivation\"\n  },\n  {\n    id: 5,\n    text: \"The way to get started is to quit talking and begin doing.\",\n    author: \"Walt Disney\",\n    category: \"action\"\n  },\n  {\n    id: 6,\n    text: \"Don't let yesterday take up too much of today.\",\n    author: \"<PERSON>\",\n    category: \"life\"\n  },\n  {\n    id: 7,\n    text: \"You learn more from failure than from success.\",\n    author: \"Unknown\",\n    category: \"success\"\n  },\n  {\n    id: 8,\n    text: \"If you are working on something that you really care about, you don't have to be pushed.\",\n    author: \"<PERSON>\",\n    category: \"motivation\"\n  },\n  {\n    id: 9,\n    text: \"Believe you can and you're halfway there.\",\n    author: \"<PERSON>\",\n    category: \"motivation\"\n  },\n  {\n    id: 10,\n    text: \"The only impossible journey is the one you never begin.\",\n    author: \"<PERSON> <PERSON>\",\n    category: \"action\"\n  },\n  {\n    id: 11,\n    text: \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    author: \"<PERSON> <PERSON>\",\n    category: \"success\"\n  },\n  {\n    id: 12,\n    text: \"The greatest glory in living lies not in never falling, but in rising every time we fall.\",\n    author: \"<PERSON> Mandela\",\n    category: \"life\"\n  },\n  {\n    id: 13,\n    text: \"Your time is limited, don't waste it living someone else's life.\",\n    author: \"Steve Jobs\",\n    category: \"inspiration\"\n  },\n  {\n    id: 14,\n    text: \"The only thing we have to fear is fear itself.\",\n    author: \"Franklin D. Roosevelt\",\n    category: \"courage\"\n  },\n  {\n    id: 15,\n    text: \"In the middle of difficulty lies opportunity.\",\n    author: \"Albert Einstein\",\n    category: \"opportunity\"\n  },\n  {\n    id: 16,\n    text: \"It does not matter how slowly you go as long as you do not stop.\",\n    author: \"Confucius\",\n    category: \"perseverance\"\n  },\n  {\n    id: 17,\n    text: \"Everything you've ever wanted is on the other side of fear.\",\n    author: \"George Addair\",\n    category: \"courage\"\n  },\n  {\n    id: 18,\n    text: \"Dream big and dare to fail.\",\n    author: \"Norman Vaughan\",\n    category: \"dreams\"\n  },\n  {\n    id: 19,\n    text: \"The best time to plant a tree was 20 years ago. The second best time is now.\",\n    author: \"Chinese Proverb\",\n    category: \"action\"\n  },\n  {\n    id: 20,\n    text: \"Don't watch the clock; do what it does. Keep going.\",\n    author: \"Sam Levenson\",\n    category: \"perseverance\"\n  },\n  {\n    id: 21,\n    text: \"You miss 100% of the shots you don't take.\",\n    author: \"Wayne Gretzky\",\n    category: \"motivation\"\n  },\n  {\n    id: 22,\n    text: \"Whether you think you can or you think you can’t, you’re right.\",\n    author: \"Henry Ford\",\n    category: \"mindset\"\n  },\n  {\n    id: 23,\n    text: \"I have not failed. I've just found 10,000 ways that won't work.\",\n    author: \"Thomas Edison\",\n    category: \"perseverance\"\n  },\n  {\n    id: 24,\n    text: \"Be yourself; everyone else is already taken.\",\n    author: \"Oscar Wilde\",\n    category: \"individuality\"\n  },\n  {\n    id: 25,\n    text: \"If you tell the truth, you don't have to remember anything.\",\n    author: \"Mark Twain\",\n    category: \"honesty\"\n  },\n  {\n    id: 26,\n    text: \"A journey of a thousand miles begins with a single step.\",\n    author: \"Lao Tzu\",\n    category: \"journey\"\n  },\n  {\n    id: 27,\n    text: \"That which does not kill us makes us stronger.\",\n    author: \"Friedrich Nietzsche\",\n    category: \"resilience\"\n  },\n  {\n    id: 28,\n    text: \"Keep calm and carry on.\",\n    author: \"Winston Churchill\",\n    category: \"motivation\"\n  },\n  {\n    id: 29,\n    text: \"Those who dare to fail miserably can achieve greatly.\",\n    author: \"John F. Kennedy\",\n    category: \"courage\"\n  },\n  {\n    id: 30,\n    text: \"The purpose of our lives is to be happy.\",\n    author: \"Dalai Lama\",\n    category: \"life\"\n  },\n  {\n    id: 31,\n    text: \"Get busy living or get busy dying.\",\n    author: \"Stephen King\",\n    category: \"life\"\n  },\n  {\n    id: 32,\n    text: \"You only live once, but if you do it right, once is enough.\",\n    author: \"Mae West\",\n    category: \"life\"\n  },\n  {\n    id: 33,\n    text: \"To live is the rarest thing in the world. Most people exist, that is all.\",\n    author: \"Oscar Wilde\",\n    category: \"life\"\n  },\n  {\n    id: 34,\n    text: \"That’s one small step for man, one giant leap for mankind.\",\n    author: \"Neil Armstrong\",\n    category: \"inspiration\"\n  },\n  {\n    id: 35,\n    text: \"If you want to lift yourself up, lift up someone else.\",\n    author: \"Booker T. Washington\",\n    category: \"kindness\"\n  },\n  {\n    id: 36,\n    text: \"We accept the love we think we deserve.\",\n    author: \"Stephen Chbosky\",\n    category: \"love\"\n  },\n  {\n    id: 37,\n    text: \"Imperfection is beauty, madness is genius and it's better to be absolutely ridiculous than absolutely boring.\",\n    author: \"Marilyn Monroe\",\n    category: \"beauty\"\n  },\n  {\n    id: 38,\n    text: \"It is never too late to be what you might have been.\",\n    author: \"George Eliot\",\n    category: \"inspiration\"\n  },\n  {\n    id: 39,\n    text: \"There is no substitute for hard work.\",\n    author: \"Thomas Edison\",\n    category: \"work\"\n  },\n  {\n    id: 40,\n    text: \"The only limit to our realization of tomorrow is our doubts of today.\",\n    author: \"Franklin D. Roosevelt\",\n    category: \"confidence\"\n  },\n  {\n    id: 41,\n    text: \"Success usually comes to those who are too busy to be looking for it.\",\n    author: \"Henry David Thoreau\",\n    category: \"success\"\n  },\n  {\n    id: 42,\n    text: \"If you can dream it, you can do it.\",\n    author: \"Walt Disney\",\n    category: \"dreams\"\n  },\n  {\n    id: 43,\n    text: \"Do one thing every day that scares you.\",\n    author: \"Eleanor Roosevelt\",\n    category: \"courage\"\n  },\n  {\n    id: 44,\n    text: \"Act as if what you do makes a difference. It does.\",\n    author: \"William James\",\n    category: \"action\"\n  },\n  {\n    id: 45,\n    text: \"What we think, we become.\",\n    author: \"Buddha\",\n    category: \"mindset\"\n  },\n  {\n    id: 46,\n    text: \"The secret of getting ahead is getting started.\",\n    author: \"Mark Twain\",\n    category: \"action\"\n  },\n  {\n    id: 47,\n    text: \"Not everything that is faced can be changed, but nothing can be changed until it is faced.\",\n    author: \"James Baldwin\",\n    category: \"change\"\n  },\n  {\n    id: 48,\n    text: \"You are never too old to set another goal or to dream a new dream.\",\n    author: \"C.S. Lewis\",\n    category: \"dreams\"\n  },\n  {\n    id: 49,\n    text: \"Strive not to be a success, but rather to be of value.\",\n    author: \"Albert Einstein\",\n    category: \"value\"\n  },\n  {\n    id: 50,\n    text: \"When one door of happiness closes, another opens.\",\n    author: \"Helen Keller\",\n    category: \"hope\"\n  }\n];\n\nexport const getRandomQuotes = (count: number = 3): Quote[] => {\n  const shuffled = [...quotesData].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\nexport const getQuotesByTopic = (topic: string, count: number = 3): Quote[] => {\n  const searchTerm = topic.toLowerCase();\n  \n  const filteredQuotes = quotesData.filter(quote => \n    quote.category.toLowerCase().includes(searchTerm) || \n    quote.text.toLowerCase().includes(searchTerm) ||\n    quote.author.toLowerCase().includes(searchTerm)\n  );\n  \n  // If no matches found, return random quotes\n  if (filteredQuotes.length === 0) {\n    return getRandomQuotes(count);\n  }\n  \n  const shuffled = filteredQuotes.sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\nexport const getAllCategories = (): string[] => {\n  const categories = quotesData.map(quote => quote.category);\n  return [...new Set(categories)].sort();\n};\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,MAAM,kBAAkB,CAAC,QAAgB,CAAC;IAC/C,MAAM,WAAW;WAAI;KAAW,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC7D,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,MAAM,mBAAmB,CAAC,OAAe,QAAgB,CAAC;IAC/D,MAAM,aAAa,MAAM,WAAW;IAEpC,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,QACvC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eACtC,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,MAAM,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;IAGtC,4CAA4C;IAC5C,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,OAAO,gBAAgB;IACzB;IAEA,MAAM,WAAW,eAAe,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC5D,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,MAAM,mBAAmB;IAC9B,MAAM,aAAa,WAAW,GAAG,CAAC,CAAA,QAAS,MAAM,QAAQ;IACzD,OAAO;WAAI,IAAI,IAAI;KAAY,CAAC,IAAI;AACtC", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Nexium-Internship/Nexium_Ahmad_Assign1/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport * as z from 'zod'\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\"\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\"\nimport { Input } from \"@/components/ui/input\"\nimport { Quote, RefreshCw } from \"lucide-react\"\nimport { Quote as QuoteType, getQuotesByTopic, getRandomQuotes } from \"@/data/quotes\"\n\nconst formSchema = z.object({\n  topic: z.string().min(1, { message: \"Topic is required.\" }),\n})\n\nexport default function QuoteGenerator() {\n  const [quotes, setQuotes] = useState<QuoteType[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: { topic: \"\" },\n  })\n\n  function onSubmit(values: z.infer<typeof formSchema>) {\n    setIsLoading(true)\n    setTimeout(() => {\n      setQuotes(getQuotesByTopic(values.topic, 3))\n      setIsLoading(false)\n    }, 1000)\n  }\n\n  const generateNewQuotes = () => {\n    setIsLoading(true)\n    setTimeout(() => {\n      setQuotes(getRandomQuotes(3))\n      setIsLoading(false)\n    }, 500)\n  }\n\n  return (\n    <div className=\"min-h-screen p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl font-bold mb-2 flex items-center justify-center gap-2 text-zinc-100\">\n            <Quote className=\"h-9 w-8 text-indigo-500\" /> Quote Generator\n          </h1>\n          <p className=\"text-yellow-100\">Get inspired with motivational quotes based on your topic</p>\n        </div>\n\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <CardTitle>Generate Quotes</CardTitle>\n            <CardDescription>\n              Enter a topic to get related motivational quotes\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Form {...form}>\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n                <FormField\n                  control={form.control}\n                  name=\"topic\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Topic</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"e.g., motivation, success, life\" {...field} />\n                      </FormControl>\n                      <FormDescription>\n                        Enter any topic for related quotes, or leave blank for random.\n                      </FormDescription>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n                <div className=\"flex gap-2\">\n                  <Button\n                    type=\"submit\"\n                    disabled={isLoading}\n                    className=\"flex-1 cursor-pointer\"\n                  >\n                    {isLoading ? \"Generating...\" : \"Generate Quotes\"}\n                  </Button>\n                  {quotes.length > 0 && (\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      onClick={generateNewQuotes}\n                      disabled={isLoading}\n                      className=\"cursor-pointer\"\n                    >\n                      <RefreshCw className=\"h-4 w-4\" />\n                    </Button>\n                  )}\n                </div>\n              </form>\n            </Form>\n          </CardContent>\n        </Card>\n\n        {quotes.length > 0 ? (\n          <div className=\"grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\">\n            {quotes.map((q) => (\n              <Card key={q.id}>\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start gap-4\">\n                    <Quote className=\"h-6 w-6 text-indigo-500 mt-1\" />\n                    <div>\n                      <blockquote className=\"text-lg font-medium mb-3 leading-relaxed\">\n                        &ldquo;{q.text}&ldquo;\n                      </blockquote>\n                      <div className=\"flex items-center justify-between\">\n                        <cite className=\"text-sm font-medium\">— {q.author}</cite>\n                        <span className=\"text-xs bg-indigo-200 text-indigo-900 px-2 py-1 rounded-full\">\n                          {q.category}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : (\n          !isLoading && (\n            <Card className=\"text-center py-12\">\n              <CardContent>\n                <Quote className=\"h-12 w-12 text-indigo-500 mx-auto mb-4\" />\n                <p>Enter a topic above to generate inspiring quotes!</p>\n              </CardContent>\n            </Card>\n          )\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA;AACA;AAjBA;;;;;;;;;;;;AAmBA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,SAAQ,AAAD,EAAE;IAC1B,OAAO,CAAA,GAAA,kIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;AAC3D;AAEe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA8B;QAC/C,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YAAE,OAAO;QAAG;IAC7B;IAEA,SAAS,SAAS,MAAkC;QAClD,aAAa;QACb,WAAW;YACT,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,KAAK,EAAE;YACzC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,WAAW;YACT,UAAU,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;YAC1B,aAAa;QACf,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;sCAE/C,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;;8BAGjC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAE,GAAG,IAAI;0CACZ,cAAA,8OAAC;oCAAK,UAAU,KAAK,YAAY,CAAC;oCAAW,WAAU;;sDACrD,8OAAC,gIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sEACP,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,aAAY;gEAAmC,GAAG,KAAK;;;;;;;;;;;sEAEhE,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;sEAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,YAAY,kBAAkB;;;;;;gDAEhC,OAAO,MAAM,GAAG,mBACf,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;oDACV,WAAU;8DAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASlC,OAAO,MAAM,GAAG,kBACf,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,kBACX,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;8DACC,8OAAC;oDAAW,WAAU;;wDAA2C;wDACvD,EAAE,IAAI;wDAAC;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAsB;gEAAG,EAAE,MAAM;;;;;;;sEACjD,8OAAC;4DAAK,WAAU;sEACb,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAXZ,EAAE,EAAE;;;;;;;;;2BAqBnB,CAAC,2BACC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}]}